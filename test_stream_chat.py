#!/usr/bin/env python3
"""
测试流式聊天功能
"""
import asyncio
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from faire2api import create_agent_instance, stream_chat_response, start_chat_optimized


async def test_stream_response():
    """测试流式响应功能"""
    print("🧪 测试流式响应功能")
    print("=" * 50)
    
    # 创建代理实例
    agent_id = create_agent_instance()
    if not agent_id:
        print("❌ 无法创建代理实例")
        return
    
    # 测试查询
    test_queries = [
        "你好，请简单介绍一下自己",
        "写一个简单的Python Hello World程序",
        "解释一下什么是机器学习"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n📝 测试查询 {i}: {query}")
        print("-" * 30)
        
        try:
            # 使用流式响应
            response_parts = []
            async for chunk in stream_chat_response(agent_id, query):
                print(chunk, end="", flush=True)
                response_parts.append(chunk)
            
            print(f"\n✅ 查询 {i} 完成，共收到 {len(response_parts)} 个响应片段")
            
        except Exception as e:
            print(f"\n❌ 查询 {i} 失败: {e}")
        
        # 等待一下再进行下一个测试
        if i < len(test_queries):
            print("\n⏳ 等待3秒后进行下一个测试...")
            await asyncio.sleep(3)


async def test_optimized_chat():
    """测试优化版聊天功能"""
    print("\n🚀 测试优化版聊天功能")
    print("=" * 50)
    
    # 创建代理实例
    agent_id = create_agent_instance()
    if not agent_id:
        print("❌ 无法创建代理实例")
        return
    
    # 测试查询
    query = "请写一个简单的JavaScript函数来计算两个数的和"
    
    try:
        await start_chat_optimized(agent_id, query)
        print("\n✅ 优化版聊天测试完成")
    except Exception as e:
        print(f"\n❌ 优化版聊天测试失败: {e}")


async def main():
    """主测试函数"""
    print("🧪 Fairies AI 流式聊天测试")
    print("=" * 60)
    
    print("\n📋 选择测试模式:")
    print("1. 测试流式响应生成器")
    print("2. 测试优化版聊天功能")
    print("3. 运行所有测试")
    
    try:
        choice = input("\n请选择测试模式 (1/2/3，默认3): ").strip() or "3"
        
        if choice == "1":
            await test_stream_response()
        elif choice == "2":
            await test_optimized_chat()
        elif choice == "3":
            await test_stream_response()
            await asyncio.sleep(2)
            await test_optimized_chat()
        else:
            print("❌ 无效选择，运行所有测试")
            await test_stream_response()
            await asyncio.sleep(2)
            await test_optimized_chat()
            
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试运行出错: {e}")


if __name__ == "__main__":
    asyncio.run(main())
