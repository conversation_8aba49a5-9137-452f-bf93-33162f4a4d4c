#!/usr/bin/env python3
"""
即时聊天演示 - 应用启动时预建立连接，实现超快响应
"""
import asyncio
import time
from faire2api_fast import initialize_connection, fast_stream_chat, close_connection


async def instant_demo():
    """即时响应演示"""
    print("⚡ 即时聊天演示")
    print("=" * 60)
    print("🔄 正在启动并预建立连接...")
    
    # 应用启动时就建立连接
    start_init = time.time()
    if not await initialize_connection():
        print("❌ 初始化失败")
        return
    end_init = time.time()
    
    print(f"✅ 预连接完成，耗时 {end_init - start_init:.2f} 秒")
    print("🚀 现在可以享受超快响应了！")
    print("=" * 60)
    
    # 测试几个快速问题
    test_questions = [
        "你好",
        "1+1=?", 
        "今天星期几？",
        "写个Hello World"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n📝 测试 {i}: {question}")
        print("🤖 AI: ", end="", flush=True)
        
        # 记录响应时间
        start_time = time.time()
        first_response_time = None
        
        async for chunk in fast_stream_chat(question):
            if first_response_time is None and chunk.strip():
                first_response_time = time.time()
            print(chunk, end="", flush=True)
        
        if first_response_time:
            response_delay = first_response_time - start_time
            print(f"\n⚡ 首次响应延迟: {response_delay:.3f} 秒")
        
        # 短暂等待
        await asyncio.sleep(1)
    
    print(f"\n🎉 演示完成！预连接让响应速度大幅提升！")
    await close_connection()


async def interactive_instant_chat():
    """交互式即时聊天"""
    print("💬 交互式即时聊天")
    print("=" * 60)
    print("🔄 正在预建立连接...")
    
    # 预建立连接
    if not await initialize_connection():
        print("❌ 初始化失败")
        return
    
    print("✅ 连接已就绪，享受超快响应！")
    print("输入 'quit' 退出聊天")
    print("=" * 60)
    
    try:
        while True:
            user_input = input("\n👤 你: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出', 'q']:
                print("👋 再见！")
                break
            
            if not user_input:
                continue
            
            print("🤖 AI: ", end="", flush=True)
            
            # 记录响应时间
            start_time = time.time()
            first_response_time = None
            
            async for chunk in fast_stream_chat(user_input):
                if first_response_time is None and chunk.strip():
                    first_response_time = time.time()
                print(chunk, end="", flush=True)
            
            if first_response_time:
                response_delay = first_response_time - start_time
                print(f"\n⚡ 响应延迟: {response_delay:.3f}秒")
    
    except KeyboardInterrupt:
        print("\n👋 用户中断")
    finally:
        await close_connection()


async def compare_with_without_preconnection():
    """对比有无预连接的响应速度"""
    print("📊 预连接效果对比测试")
    print("=" * 60)
    
    test_query = "你好"
    
    # 测试1：不使用预连接（模拟传统方式）
    print("🐌 测试1: 传统方式（每次建立新连接）")
    print("注意：这会比较慢...")
    
    from faire2api_fast import create_agent_instance
    import websockets.legacy.client as ws_client
    import json
    
    start_time = time.time()
    
    try:
        # 创建代理实例
        agent_id = create_agent_instance()
        
        # 建立连接
        headers_dict = {
            "Cookie": "gbuuid=ef1a58c4-d2ff-4cd1-ac30-1b1b5a836384; useKeys=false; osType=web; session=fq7xMIUnVWdGTea9W9tjKij4c2f2hGMxcYKLGYnINMM",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0",
            "Origin": "https://fairies.ai"
        }
        
        async with ws_client.connect("wss://fairies.ai/api/agent/ws", extra_headers=headers_dict) as websocket:
            # 发送消息
            user_message = {
                "type": "USER_MESSAGE",
                "agent_id": agent_id,
                "observation": {
                    "message": {
                        "user_query": test_query,
                        "mode": "main",
                        "files": [],
                        "memories": [],
                        "model": "claude-sonnet-4-20250514",
                        "task_id": None,
                        "workspace_path": "/"
                    }
                }
            }
            
            await websocket.send(json.dumps(user_message))
            first_response_time = time.time()
            
            print(f"🤖 AI: ", end="", flush=True)
            
            # 简单处理响应
            async for message in websocket:
                try:
                    parsed_message = json.loads(message)
                    msg_type = parsed_message.get("type")
                    if msg_type == "CHAT_MESSAGE":
                        inner_msg = parsed_message.get("message", {}).get("message", {})
                        inner_type = inner_msg.get("type")
                        
                        if inner_type == "response.output_text.delta":
                            delta_text = inner_msg.get("delta", "")
                            if delta_text:
                                print(delta_text, end="", flush=True)
                        elif inner_type == "response.output_item.done":
                            break
                except:
                    continue
        
        traditional_time = first_response_time - start_time
        print(f"\n⏱️ 传统方式首次响应时间: {traditional_time:.3f}秒")
        
    except Exception as e:
        print(f"❌ 传统方式测试失败: {e}")
        traditional_time = float('inf')
    
    await asyncio.sleep(2)
    
    # 测试2：使用预连接
    print(f"\n⚡ 测试2: 预连接方式")
    
    # 预建立连接
    await initialize_connection()
    
    start_time = time.time()
    first_response_time = None
    
    print(f"🤖 AI: ", end="", flush=True)
    
    async for chunk in fast_stream_chat(test_query):
        if first_response_time is None and chunk.strip():
            first_response_time = time.time()
        print(chunk, end="", flush=True)
    
    if first_response_time:
        preconnect_time = first_response_time - start_time
        print(f"\n⏱️ 预连接方式首次响应时间: {preconnect_time:.3f}秒")
        
        if traditional_time != float('inf'):
            improvement = traditional_time - preconnect_time
            improvement_percent = (improvement / traditional_time) * 100
            print(f"\n🚀 性能提升: {improvement:.3f}秒 ({improvement_percent:.1f}%)")
    
    await close_connection()


async def main():
    """主函数"""
    print("⚡ 即时聊天系统 - 预连接演示")
    print("=" * 70)
    
    print("\n📋 选择演示模式:")
    print("1. 即时响应演示")
    print("2. 交互式即时聊天")
    print("3. 预连接效果对比")
    
    try:
        choice = input("\n请选择模式 (1/2/3，默认1): ").strip() or "1"
        
        if choice == "1":
            await instant_demo()
        elif choice == "2":
            await interactive_instant_chat()
        elif choice == "3":
            await compare_with_without_preconnection()
        else:
            print("❌ 无效选择，运行即时响应演示")
            await instant_demo()
            
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
    finally:
        await close_connection()


if __name__ == "__main__":
    asyncio.run(main())
