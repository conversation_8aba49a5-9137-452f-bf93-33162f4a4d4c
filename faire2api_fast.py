#!/usr/bin/env python3
"""
优化版 Fairies AI 客户端 - 减少延迟，提高响应速度
"""
import asyncio
import json
import requests
import websockets
import time
import sys
from typing import AsyncGenerator, Optional

# --- 配置信息 ---
AGENT_PROTO_ID = "4f18843d-8823-4151-99e2-00fa87eec890"
FULL_COOKIE_STRING = (
"gbuuid=ef1a58c4-d2ff-4cd1-ac30-1b1b5a836384; useKeys=false; osType=web; session=fq7xMIUnVWdGTea9W9tjKij4c2f2hGMxcYKLGYnINMM; mp_5cfe752f1633fdf743200078293ddde9_mixpanel=%7B%22distinct_id%22%3A%2216adf946-14ef-48fd-aa41-1a93d24b6984%22%2C%22%24device_id%22%3A%22296cf93b-**************-c0633c9b8ec2%22%2C%22%24initial_referrer%22%3A%22%24direct%22%2C%22%24initial_referring_domain%22%3A%22%24direct%22%2C%22__mps%22%3A%7B%7D%2C%22__mpso%22%3A%7B%7D%2C%22__mpus%22%3A%7B%7D%2C%22__mpa%22%3A%7B%7D%2C%22__mpu%22%3A%7B%7D%2C%22__mpr%22%3A%5B%5D%2C%22__mpap%22%3A%5B%5D%2C%22%24search_engine%22%3A%22google%22%2C%22%24user_id%22%3A%2216adf946-14ef-48fd-aa41-1a93d24b6984%22%7D; ph_phc_or5z2MyKAus4GbUsu0KmoHbjPYUeoX6xdObszTbBM3a_posthog=%7B%22distinct_id%22%3A%2216adf946-14ef-48fd-aa41-1a93d24b6984%22%2C%22%24sesid%22%3A%5B1753841116234%2C%2201985907-b5c7-7374-b5d9-01b4b5123bb6%22%2C1753840334279%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22https%3A%2F%2Ffairies.ai%2F%22%7D%7D"
)

# API 端点
CREATE_INSTANCE_URL = "https://fairies.ai/api/agent/create_instance"
WEBSOCKET_URL = "wss://fairies.ai/api/agent/ws"

# 全局变量
_websocket_connection = None
_agent_id = None
_connection_lock = asyncio.Lock()

headers = {
    "Content-Type": "application/json",
    "Cookie": FULL_COOKIE_STRING,
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0"
}


def create_agent_instance():
    """创建AI代理实例"""
    print("🚀 正在创建 AI 代理实例...")
    payload = {"agent_proto_id": AGENT_PROTO_ID}
    try:
        response = requests.post(CREATE_INSTANCE_URL, headers=headers, json=payload, timeout=10)
        response.raise_for_status()
        data = response.json()
        agent_id = data.get("id")
        if not agent_id:
            print("❌ 错误：响应中未找到 'id' 字段。")
            return None
        print(f"✅ 实例创建成功 (ID: {agent_id[:8]}...)")
        return agent_id
    except requests.exceptions.RequestException as e:
        print(f"❌ 创建代理实例失败: {e}")
        return None


async def get_websocket_connection():
    """获取或创建WebSocket连接（连接池模式）"""
    global _websocket_connection, _agent_id
    
    async with _connection_lock:
        # 如果连接不存在或已关闭，创建新连接
        if _websocket_connection is None or _websocket_connection.closed:
            print("🔗 建立新的WebSocket连接...")
            
            # 创建代理实例
            if _agent_id is None:
                _agent_id = create_agent_instance()
                if not _agent_id:
                    raise Exception("无法创建代理实例")
            
            # 建立WebSocket连接
            import websockets.legacy.client as ws_client
            headers_dict = {
                "Cookie": FULL_COOKIE_STRING,
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0",
                "Origin": "https://fairies.ai",
                "Sec-WebSocket-Protocol": "chat"
            }
            
            try:
                _websocket_connection = await ws_client.connect(
                    WEBSOCKET_URL, 
                    extra_headers=headers_dict,
                    ping_interval=20,  # 保持连接活跃
                    ping_timeout=10,
                    close_timeout=5
                )
                print("✅ WebSocket连接已建立")
            except Exception as e:
                print(f"❌ WebSocket连接失败: {e}")
                _websocket_connection = None
                raise
        
        return _websocket_connection, _agent_id


async def fast_stream_chat(query: str) -> AsyncGenerator[str, None]:
    """快速流式聊天 - 使用持久连接减少延迟"""
    try:
        # 获取连接
        websocket, agent_id = await get_websocket_connection()
        
        # 准备消息
        user_message = {
            "type": "USER_MESSAGE",
            "agent_id": agent_id,
            "observation": {
                "message": {
                    "user_query": query,
                    "mode": "main",
                    "files": [],
                    "memories": [],
                    "model": "claude-sonnet-4-20250514",
                    "task_id": None,
                    "workspace_path": "/"
                }
            }
        }
        
        # 立即发送消息
        await websocket.send(json.dumps(user_message))
        yield "🚀 消息已发送，等待回复...\n"
        
        # 处理响应
        is_assistant_turn = False
        response_started = False
        
        async for message in websocket:
            try:
                # 解析消息
                try:
                    inner_json_string = json.loads(message)
                    parsed_message = json.loads(inner_json_string)
                except (json.JSONDecodeError, TypeError):
                    parsed_message = json.loads(message)

                msg_type = parsed_message.get("type")
                if msg_type == "CHAT_MESSAGE":
                    inner_msg = parsed_message.get("message", {}).get("message", {})
                    inner_type = inner_msg.get("type")
                    role = inner_msg.get("item", {}).get("role")

                    # 检测 assistant 回合开始
                    if inner_type == "response.output_item.added" and role == "assistant":
                        is_assistant_turn = True
                        if not response_started:
                            yield "🤖 AI: "
                            response_started = True

                    # 立即输出文本片段
                    elif is_assistant_turn and inner_type == "response.output_text.delta":
                        delta_text = inner_msg.get("delta", "")
                        if delta_text:
                            yield delta_text

                    # assistant 回合结束
                    elif inner_type == "response.output_item.done" and role == "assistant":
                        is_assistant_turn = False
                        yield "\n"
                        break
                        
            except json.JSONDecodeError:
                continue
                
    except Exception as e:
        yield f"❌ 连接错误: {e}\n"
        # 重置连接以便下次重新建立
        global _websocket_connection
        _websocket_connection = None


async def close_connection():
    """关闭WebSocket连接"""
    global _websocket_connection
    if _websocket_connection and not _websocket_connection.closed:
        await _websocket_connection.close()
        _websocket_connection = None
        print("🔌 WebSocket连接已关闭")


async def fast_chat_session():
    """快速聊天会话"""
    print("⚡ 快速流式聊天模式")
    print("=" * 50)
    print("特点：使用持久连接，减少延迟")
    print("输入 'quit' 退出")
    print("=" * 50)
    
    try:
        while True:
            user_input = input("\n👤 你: ").strip()
            if user_input.lower() in ['quit', 'exit', '退出']:
                break
            
            if not user_input:
                continue
            
            start_time = time.time()
            
            async for chunk in fast_stream_chat(user_input):
                print(chunk, end="", flush=True)
            
            end_time = time.time()
            print(f"⏱️ 响应时间: {end_time - start_time:.2f}秒")
            
    except KeyboardInterrupt:
        print("\n👋 用户中断")
    finally:
        await close_connection()


async def benchmark_response_time():
    """基准测试响应时间"""
    print("📊 响应时间基准测试")
    print("=" * 50)
    
    test_queries = [
        "你好",
        "1+1=?",
        "今天天气怎么样？",
        "写一个Hello World",
        "解释什么是AI"
    ]
    
    total_times = []
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n🧪 测试 {i}/5: {query}")
        
        start_time = time.time()
        first_response_time = None
        
        async for chunk in fast_stream_chat(query):
            if first_response_time is None:
                first_response_time = time.time()
            print(chunk, end="", flush=True)
        
        end_time = time.time()
        
        if first_response_time:
            response_delay = first_response_time - start_time
            total_time = end_time - start_time
            total_times.append(response_delay)
            
            print(f"\n⏱️ 首次响应延迟: {response_delay:.2f}秒")
            print(f"⏱️ 总响应时间: {total_time:.2f}秒")
        
        await asyncio.sleep(1)  # 短暂等待
    
    if total_times:
        avg_time = sum(total_times) / len(total_times)
        print(f"\n📈 平均首次响应延迟: {avg_time:.2f}秒")
    
    await close_connection()


async def main():
    """主函数"""
    print("⚡ Fairies AI 快速流式聊天客户端")
    print("=" * 60)
    
    print("\n📋 选择模式:")
    print("1. 快速聊天会话")
    print("2. 响应时间基准测试")
    print("3. 单次快速测试")
    
    try:
        choice = input("\n请选择模式 (1/2/3，默认1): ").strip() or "1"
        
        if choice == "1":
            await fast_chat_session()
        elif choice == "2":
            await benchmark_response_time()
        elif choice == "3":
            query = input("请输入测试问题: ").strip() or "你好"
            print(f"\n👤 你: {query}")
            
            start_time = time.time()
            async for chunk in fast_stream_chat(query):
                print(chunk, end="", flush=True)
            end_time = time.time()
            
            print(f"\n⏱️ 总响应时间: {end_time - start_time:.2f}秒")
            await close_connection()
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
    finally:
        await close_connection()


if __name__ == "__main__":
    asyncio.run(main())
