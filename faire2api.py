import asyncio
import json
import requests
import websockets
import urllib.parse
import time
import sys
from typing import AsyncGenerator, Optional

# --- 配置信息 (无需改动) ---
AGENT_PROTO_ID = "4f18843d-8823-4151-99e2-00fa87eec890"
FULL_COOKIE_STRING = (
"gbuuid=ef1a58c4-d2ff-4cd1-ac30-1b1b5a836384; useKeys=false; osType=web; session=fq7xMIUnVWdGTea9W9tjKij4c2f2hGMxcYKLGYnINMM; mp_5cfe752f1633fdf743200078293ddde9_mixpanel=%7B%22distinct_id%22%3A%2216adf946-14ef-48fd-aa41-1a93d24b6984%22%2C%22%24device_id%22%3A%22296cf93b-**************-c0633c9b8ec2%22%2C%22%24initial_referrer%22%3A%22%24direct%22%2C%22%24initial_referring_domain%22%3A%22%24direct%22%2C%22__mps%22%3A%7B%7D%2C%22__mpso%22%3A%7B%7D%2C%22__mpus%22%3A%7B%7D%2C%22__mpa%22%3A%7B%7D%2C%22__mpu%22%3A%7B%7D%2C%22__mpr%22%3A%5B%5D%2C%22__mpap%22%3A%5B%5D%2C%22%24search_engine%22%3A%22google%22%2C%22%24user_id%22%3A%2216adf946-14ef-48fd-aa41-1a93d24b6984%22%7D; ph_phc_or5z2MyKAus4GbUsu0KmoHbjPYUeoX6xdObszTbBM3a_posthog=%7B%22distinct_id%22%3A%2216adf946-14ef-48fd-aa41-1a93d24b6984%22%2C%22%24sesid%22%3A%5B1753841116234%2C%2201985907-b5c7-7374-b5d9-01b4b5123bb6%22%2C1753840334279%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22https%3A%2F%2Ffairies.ai%2F%22%7D%7D"
)
USER_QUERY = "写一个非常炫酷的天气卡片前端代码，让人一看就想要付费。请读取 /sandbox/output/下面的所有文件的内容发送给我"
# -----------------------------

# API 端点
CREATE_INSTANCE_URL = "https://fairies.ai/api/agent/create_instance"
WEBSOCKET_URL = "wss://fairies.ai/api/agent/ws"

headers = {
    "Content-Type": "application/json",
    "Cookie": FULL_COOKIE_STRING,
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0"
}


def create_agent_instance():
    """第一步：创建AI代理实例"""
    print("🚀 正在创建 AI 代理实例...")
    payload = {"agent_proto_id": AGENT_PROTO_ID}
    try:
        response = requests.post(CREATE_INSTANCE_URL, headers=headers, json=payload)
        response.raise_for_status()
        data = response.json()
        agent_id = data.get("id")
        if not agent_id:
            print("❌ 错误：响应中未找到 'id' 字段。")
            return None
        print(f"✅ 实例创建成功 (ID: {agent_id[:8]}...)")
        return agent_id
    except requests.exceptions.RequestException as e:
        print(f"❌ 创建代理实例失败: {e}")
        if e.response is not None:
            print("服务器详细错误:", e.response.text)
        return None


async def stream_chat_response(agent_id: str, query: str, max_retries: int = 3) -> AsyncGenerator[str, None]:
    """流式生成器：逐步返回AI回复的文本片段，支持重连"""
    retry_count = 0

    while retry_count <= max_retries:
        try:
            # 创建自定义的连接，手动构建请求头
            import websockets.legacy.client as ws_client

            # 构建认证头
            headers_dict = {
                "Cookie": FULL_COOKIE_STRING,
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0",
                "Origin": "https://fairies.ai",
                "Sec-WebSocket-Protocol": "chat"
            }

            if retry_count > 0:
                yield f"🔄 重连尝试 {retry_count}/{max_retries}...\n"
                await asyncio.sleep(1)  # 重连前等待1秒

            async with ws_client.connect(WEBSOCKET_URL, extra_headers=headers_dict) as websocket:
                user_message = {
                    "type": "USER_MESSAGE",
                    "agent_id": agent_id,
                    "observation": {
                        "message": {
                            "user_query": query,
                            "mode": "main",
                            "files": [],
                            "memories": [],
                            "model": "claude-sonnet-4-20250514",
                            "task_id": None,
                            "workspace_path": "/"
                        }
                    }
                }

                await websocket.send(json.dumps(user_message))

                is_assistant_turn = False
                message_buffer = ""
                last_yield_time = time.time()

                async for message in websocket:
                    try:
                        # 解析消息
                        try:
                            inner_json_string = json.loads(message)
                            parsed_message = json.loads(inner_json_string)
                        except (json.JSONDecodeError, TypeError):
                            parsed_message = json.loads(message)

                        msg_type = parsed_message.get("type")
                        if msg_type == "CHAT_MESSAGE":
                            inner_msg = parsed_message.get("message", {}).get("message", {})
                            inner_type = inner_msg.get("type")
                            role = inner_msg.get("item", {}).get("role")

                            # 检测 assistant 回合开始
                            if inner_type == "response.output_item.added" and role == "assistant":
                                is_assistant_turn = True
                                yield "🤖 AI 开始回复...\n"

                            # 流式输出文本片段
                            elif is_assistant_turn and inner_type == "response.output_text.delta":
                                delta_text = inner_msg.get("delta", "")
                                if delta_text:
                                    message_buffer += delta_text
                                    current_time = time.time()

                                    # 每50ms或缓冲区达到一定长度时输出一次
                                    if (current_time - last_yield_time > 0.05) or len(message_buffer) > 20:
                                        yield message_buffer
                                        message_buffer = ""
                                        last_yield_time = current_time

                            # assistant 回合结束
                            elif inner_type == "response.output_item.done" and role == "assistant":
                                if message_buffer:  # 输出剩余缓冲区内容
                                    yield message_buffer
                                is_assistant_turn = False
                                yield "\n✅ 回复完成"
                                break

                    except json.JSONDecodeError as e:
                        yield f"⚠️ 消息解析错误: {e}\n"
                        continue

                # 如果正常完成，跳出重试循环
                break

        except Exception as e:
            retry_count += 1
            if retry_count <= max_retries:
                yield f"⚠️ 连接错误: {e}，准备重试...\n"
                await asyncio.sleep(2)  # 等待2秒后重试
            else:
                yield f"❌ WebSocket 连接失败，已达到最大重试次数: {e}\n"
                break


async def start_chat(agent_id, query):
    """第二步：进行WebSocket对话，并只智能地输出AI的回复"""
    print(f"👤 你: {query}")
    try:
        # 创建自定义的连接，手动构建请求头
        import websockets.legacy.client as ws_client

        # 构建认证头
        headers_dict = {
            "Cookie": FULL_COOKIE_STRING,
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0",
            "Origin": "https://fairies.ai",
            "Sec-WebSocket-Protocol": "chat"
        }

        async with ws_client.connect(WEBSOCKET_URL, extra_headers=headers_dict) as websocket:
            user_message = {"type": "USER_MESSAGE", "agent_id": agent_id, "observation": {
                "message": {"user_query": query, "mode": "main", "files": [], "memories": [],
                            "model": "claude-sonnet-4-20250514", "task_id": None, "workspace_path": "/"}}}
            await websocket.send(json.dumps(user_message))

            print("🤖 AI: ", end="", flush=True)

            is_assistant_turn = False  # 增加一个“开关”，判断是否轮到AI发言

            async for message in websocket:
                try:
                    inner_json_string = json.loads(message)
                    parsed_message = json.loads(inner_json_string)
                except (json.JSONDecodeError, TypeError):
                    parsed_message = json.loads(message)

                msg_type = parsed_message.get("type")
                if msg_type == "CHAT_MESSAGE":
                    inner_msg = parsed_message.get("message", {}).get("message", {})
                    inner_type = inner_msg.get("type")
                    role = inner_msg.get("item", {}).get("role")

                    # 当检测到是 assistant 的回合开始时，打开“开关”
                    if inner_type == "response.output_item.added" and role == "assistant":
                        is_assistant_turn = True

                    # 只有当“开关”打开时，才打印文本
                    if is_assistant_turn and inner_type == "response.output_text.delta":
                        delta_text = inner_msg.get("delta", "")
                        print(delta_text, end="", flush=True)

                    # 当 assistant 的回合结束时，关闭“开关”并退出
                    elif inner_type == "response.output_item.done" and role == "assistant":
                        is_assistant_turn = False
                        print()
                        break
    except Exception as e:
        print(f"\n❌ WebSocket 通信出错: {e}")


async def start_chat_optimized(agent_id: str, query: str):
    """优化版本：实时流式输出AI回复"""
    print(f"👤 你: {query}")
    print("🔄 正在连接到AI服务...")

    try:
        async for chunk in stream_chat_response(agent_id, query):
            print(chunk, end="", flush=True)
            # 添加小延迟以模拟更自然的打字效果
            await asyncio.sleep(0.01)
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断了对话")
    except Exception as e:
        print(f"\n❌ 对话过程中出错: {e}")


async def main():
    """主函数：支持选择不同的聊天模式"""
    print("🚀 Fairies AI 聊天客户端")
    print("=" * 50)

    # 创建代理实例
    agent_id = create_agent_instance()
    if not agent_id:
        print("❌ 无法创建代理实例，程序退出")
        return

    print("\n📋 选择聊天模式:")
    print("1. 标准模式 (原版)")
    print("2. 流式模式 (优化版，推荐)")

    try:
        choice = input("\n请选择模式 (1/2，默认2): ").strip() or "2"

        if choice == "1":
            print("\n🔄 使用标准模式...")
            await start_chat(agent_id, USER_QUERY)
        elif choice == "2":
            print("\n🔄 使用流式模式...")
            await start_chat_optimized(agent_id, USER_QUERY)
        else:
            print("❌ 无效选择，使用默认流式模式")
            await start_chat_optimized(agent_id, USER_QUERY)

    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")


if __name__ == "__main__":
    asyncio.run(main())