import asyncio
import json
import requests
import websockets
import urllib.parse
import time
import sys
from typing import AsyncGenerator, Optional

# --- 配置信息 (无需改动) ---
AGENT_PROTO_ID = "4f18843d-8823-4151-99e2-00fa87eec890"
FULL_COOKIE_STRING = (
"gbuuid=ef1a58c4-d2ff-4cd1-ac30-1b1b5a836384; useKeys=false; osType=web; session=fq7xMIUnVWdGTea9W9tjKij4c2f2hGMxcYKLGYnINMM; mp_5cfe752f1633fdf743200078293ddde9_mixpanel=%7B%22distinct_id%22%3A%2216adf946-14ef-48fd-aa41-1a93d24b6984%22%2C%22%24device_id%22%3A%22296cf93b-**************-c0633c9b8ec2%22%2C%22%24initial_referrer%22%3A%22%24direct%22%2C%22%24initial_referring_domain%22%3A%22%24direct%22%2C%22__mps%22%3A%7B%7D%2C%22__mpso%22%3A%7B%7D%2C%22__mpus%22%3A%7B%7D%2C%22__mpa%22%3A%7B%7D%2C%22__mpu%22%3A%7B%7D%2C%22__mpr%22%3A%5B%5D%2C%22__mpap%22%3A%5B%5D%2C%22%24search_engine%22%3A%22google%22%2C%22%24user_id%22%3A%2216adf946-14ef-48fd-aa41-1a93d24b6984%22%7D; ph_phc_or5z2MyKAus4GbUsu0KmoHbjPYUeoX6xdObszTbBM3a_posthog=%7B%22distinct_id%22%3A%2216adf946-14ef-48fd-aa41-1a93d24b6984%22%2C%22%24sesid%22%3A%5B1753841116234%2C%2201985907-b5c7-7374-b5d9-01b4b5123bb6%22%2C1753840334279%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22https%3A%2F%2Ffairies.ai%2F%22%7D%7D"
)
USER_QUERY = "hello"
# -----------------------------

# API 端点
CREATE_INSTANCE_URL = "https://fairies.ai/api/agent/create_instance"
WEBSOCKET_URL = "wss://fairies.ai/api/agent/ws"

headers = {
    "Content-Type": "application/json",
    "Cookie": FULL_COOKIE_STRING,
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0"
}


def create_agent_instance():
    """第一步：创建AI代理实例"""
    print("🚀 正在创建 AI 代理实例...")
    payload = {"agent_proto_id": AGENT_PROTO_ID}
    try:
        response = requests.post(CREATE_INSTANCE_URL, headers=headers, json=payload)
        response.raise_for_status()
        data = response.json()
        agent_id = data.get("id")
        if not agent_id:
            print("❌ 错误：响应中未找到 'id' 字段。")
            return None
        print(f"✅ 实例创建成功 (ID: {agent_id[:8]}...)")
        return agent_id
    except requests.exceptions.RequestException as e:
        print(f"❌ 创建代理实例失败: {e}")
        if e.response is not None:
            print("服务器详细错误:", e.response.text)
        return None


async def start_chat(agent_id, query):
    """第二步：进行WebSocket对话，并只智能地输出AI的回复"""
    print(f"👤 你: {query}")
    try:
        # 创建自定义的连接，手动构建请求头
        import websockets.legacy.client as ws_client

        # 构建认证头
        headers_dict = {
            "Cookie": FULL_COOKIE_STRING,
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0",
            "Origin": "https://fairies.ai",
            "Sec-WebSocket-Protocol": "chat"
        }

        async with ws_client.connect(WEBSOCKET_URL, extra_headers=headers_dict) as websocket:
            user_message = {"type": "USER_MESSAGE", "agent_id": agent_id, "observation": {
                "message": {"user_query": query, "mode": "main", "files": [], "memories": [],
                            "model": "claude-sonnet-4-20250514", "task_id": None, "workspace_path": "/"}}}
            await websocket.send(json.dumps(user_message))

            print("🤖 AI: ", end="", flush=True)

            is_assistant_turn = False  # 增加一个“开关”，判断是否轮到AI发言

            async for message in websocket:
                try:
                    inner_json_string = json.loads(message)
                    parsed_message = json.loads(inner_json_string)
                except (json.JSONDecodeError, TypeError):
                    parsed_message = json.loads(message)

                msg_type = parsed_message.get("type")
                if msg_type == "CHAT_MESSAGE":
                    inner_msg = parsed_message.get("message", {}).get("message", {})
                    inner_type = inner_msg.get("type")
                    role = inner_msg.get("item", {}).get("role")

                    # 当检测到是 assistant 的回合开始时，打开“开关”
                    if inner_type == "response.output_item.added" and role == "assistant":
                        is_assistant_turn = True

                    # 只有当“开关”打开时，才打印文本
                    if is_assistant_turn and inner_type == "response.output_text.delta":
                        delta_text = inner_msg.get("delta", "")
                        print(delta_text, end="", flush=True)

                    # 当 assistant 的回合结束时，关闭“开关”并退出
                    elif inner_type == "response.output_item.done" and role == "assistant":
                        is_assistant_turn = False
                        print()
                        break
    except Exception as e:
        print(f"\n❌ WebSocket 通信出错: {e}")


if __name__ == "__main__":
    agent_id = create_agent_instance()
    if agent_id:
        asyncio.run(start_chat(agent_id, USER_QUERY))