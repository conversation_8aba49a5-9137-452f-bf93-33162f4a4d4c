#!/usr/bin/env python3
"""
演示真正的流式聊天效果
"""
import asyncio
import sys
import time
from faire2api import create_agent_instance, stream_chat_response


async def demo_real_streaming():
    """演示真正的流式效果"""
    print("🚀 真正的流式聊天演示")
    print("=" * 60)
    print("注意观察：文字会逐字符实时出现，而不是批量显示")
    print("=" * 60)
    
    # 创建代理实例
    print("🔄 正在创建AI代理实例...")
    agent_id = create_agent_instance()
    if not agent_id:
        print("❌ 无法创建代理实例")
        return
    
    # 简单的测试查询
    query = "请用一句话介绍你自己"
    
    print(f"\n👤 用户: {query}")
    print("🤖 AI: ", end="", flush=True)
    
    try:
        char_count = 0
        start_time = time.time()
        
        async for chunk in stream_chat_response(agent_id, query):
            # 直接输出每个字符，不添加任何延迟
            print(chunk, end="", flush=True)
            char_count += len(chunk)
        
        end_time = time.time()
        print(f"\n\n📊 流式统计:")
        print(f"   • 总字符数: {char_count}")
        print(f"   • 总耗时: {end_time - start_time:.2f} 秒")
        print(f"   • 平均速度: {char_count / (end_time - start_time):.1f} 字符/秒")
        
    except Exception as e:
        print(f"\n❌ 演示失败: {e}")


async def compare_streaming_modes():
    """比较不同的流式模式"""
    print("\n🔬 流式模式对比测试")
    print("=" * 60)
    
    agent_id = create_agent_instance()
    if not agent_id:
        print("❌ 无法创建代理实例")
        return
    
    query = "数到5"
    
    print(f"👤 用户: {query}")
    print("\n🎯 真正的流式模式 (立即输出每个字符):")
    print("🤖 AI: ", end="", flush=True)
    
    try:
        async for chunk in stream_chat_response(agent_id, query):
            print(chunk, end="", flush=True)
            # 不添加任何延迟
        
        print("\n\n✅ 这就是真正的流式效果！")
        print("💡 特点:")
        print("   • 字符立即显示，无缓冲")
        print("   • 响应速度最快")
        print("   • 用户体验最佳")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")


async def interactive_demo():
    """交互式演示"""
    print("\n🎮 交互式流式聊天演示")
    print("=" * 60)
    print("输入 'quit' 退出演示")
    
    agent_id = create_agent_instance()
    if not agent_id:
        print("❌ 无法创建代理实例")
        return
    
    while True:
        try:
            user_input = input("\n👤 你: ").strip()
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 再见！")
                break
            
            if not user_input:
                continue
            
            print("🤖 AI: ", end="", flush=True)
            
            async for chunk in stream_chat_response(agent_id, user_input):
                print(chunk, end="", flush=True)
            
            print()  # 换行
            
        except KeyboardInterrupt:
            print("\n👋 用户中断，退出演示")
            break
        except Exception as e:
            print(f"\n❌ 错误: {e}")


async def main():
    """主演示函数"""
    print("🌟 Fairies AI 真正流式聊天演示")
    print("=" * 70)
    
    print("\n📋 选择演示模式:")
    print("1. 基础流式演示")
    print("2. 流式模式对比")
    print("3. 交互式聊天")
    print("4. 运行所有演示")
    
    try:
        choice = input("\n请选择演示模式 (1/2/3/4，默认1): ").strip() or "1"
        
        if choice == "1":
            await demo_real_streaming()
        elif choice == "2":
            await compare_streaming_modes()
        elif choice == "3":
            await interactive_demo()
        elif choice == "4":
            await demo_real_streaming()
            await asyncio.sleep(2)
            await compare_streaming_modes()
            await asyncio.sleep(2)
            await interactive_demo()
        else:
            print("❌ 无效选择，运行基础演示")
            await demo_real_streaming()
            
    except KeyboardInterrupt:
        print("\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示运行出错: {e}")


if __name__ == "__main__":
    asyncio.run(main())
